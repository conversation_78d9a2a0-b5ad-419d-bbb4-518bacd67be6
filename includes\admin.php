<?php
/**
 * Admin Dashboard - WordPress Compatible Interface
 */

defined('ABSPATH') || exit;

// Load required classes
require_once AI_STYLED_PATH . 'includes/processor.php';
require_once AI_STYLED_PATH . 'includes/gallery.php';
require_once AI_STYLED_PATH . 'includes/logger.php';

$processor = new AI_Image_Processor();

// Handle form submissions
if ($_POST) {
    if (isset($_POST['save_settings']) && wp_verify_nonce($_POST['_wpnonce'], 'ai_styled_settings')) {
        $settings = [
            'api_token' => sanitize_text_field($_POST['api_token'] ?? ''),
            'model_endpoint' => sanitize_text_field($_POST['model_endpoint'] ?? 'flux-kontext-apps/multi-image-kontext-pro'),
            'max_file_size' => absint($_POST['max_file_size'] ?? 10485760),
            'rate_limit' => absint($_POST['rate_limit'] ?? 50),
            'allowed_formats' => array_map('sanitize_text_field', $_POST['allowed_formats'] ?? ['jpg', 'jpeg', 'png', 'webp']),
            'openrouter_api_key' => sanitize_text_field($_POST['openrouter_api_key'] ?? ''),
            'openrouter_model' => sanitize_text_field($_POST['openrouter_model'] ?? 'openai/gpt-4.1'),
            'processing_mode' => 'new' // Always use new mode - manual mode removed
        ];

        foreach ($settings as $key => $value) {
            update_option("ai_styled_{$key}", $value);
        }

        // Log settings update
        AI_Styled_Logger::log_user_action(
            'settings_updated',
            'Plugin settings were updated',
            ['updated_settings' => array_keys($settings)]
        );

        echo '<div class="notice notice-success is-dismissible"><p>Settings saved successfully!</p></div>';
    }
}

// Get current settings
$settings = [
    'api_token' => get_option('ai_styled_api_token', ''),
    'model_endpoint' => get_option('ai_styled_model_endpoint', 'flux-kontext-apps/multi-image-kontext-pro'),
    'max_file_size' => get_option('ai_styled_max_file_size', 10485760),
    'rate_limit' => get_option('ai_styled_rate_limit', 50),
    'allowed_formats' => get_option('ai_styled_allowed_formats', ['jpg', 'jpeg', 'png', 'webp']),
    'openrouter_api_key' => get_option('ai_styled_openrouter_api_key', ''),
    'openrouter_model' => get_option('ai_styled_openrouter_model', 'openai/gpt-4.1'),
    'processing_mode' => 'new' // Always use new mode - manual mode removed
];

// Get data for dashboard
$identity_settings = AIStyledImagePlugin::instance()->get_identity_settings();
$overlays = $processor->get_overlays();
$gallery_stats = AI_Styled_Gallery::get_gallery_stats();
$log_stats = AI_Styled_Logger::get_log_stats();

global $wpdb;
$total_overlays = count($overlays);
$total_usage = array_sum(wp_list_pluck($overlays, 'usage_count'));
?>

<!-- WordPress Compatible Admin Dashboard -->
<div class="wrap ai-styled-admin-wrap">
    <h1 class="wp-heading-inline">AI Styled Image</h1>

    <!-- Tab Navigation -->
    <nav class="nav-tab-wrapper wp-clearfix">
        <button class="nav-tab nav-tab-active ai-nav-tab" data-tab="dashboard">
            <span class="dashicons dashicons-dashboard"></span>
            Dashboard
        </button>
        <button class="nav-tab ai-nav-tab" data-tab="gallery">
            <span class="dashicons dashicons-format-gallery"></span>
            AI Gallery
        </button>
        <button class="nav-tab ai-nav-tab" data-tab="overlays">
            <span class="dashicons dashicons-camera"></span>
            Overlays
        </button>
        <button class="nav-tab ai-nav-tab" data-tab="logs">
            <span class="dashicons dashicons-media-text"></span>
            Activity Logs
        </button>
        <button class="nav-tab ai-nav-tab" data-tab="settings">
            <span class="dashicons dashicons-admin-settings"></span>
            Settings
        </button>
    </nav>

    <!-- Stats Header -->
    <div class="ai-stats-header">
        <div class="ai-stats-grid">
            <div class="ai-stat-card">
                <div class="ai-stat-icon ai-stat-icon-blue">
                    <span class="dashicons dashicons-format-gallery"></span>
                </div>
                <div class="ai-stat-content">
                    <div class="ai-stat-number"><?php echo $gallery_stats['total_images']; ?></div>
                    <div class="ai-stat-label">AI Images</div>
                </div>
            </div>

            <div class="ai-stat-card">
                <div class="ai-stat-icon ai-stat-icon-green">
                    <span class="dashicons dashicons-camera"></span>
                </div>
                <div class="ai-stat-content">
                    <div class="ai-stat-number"><?php echo $total_overlays; ?></div>
                    <div class="ai-stat-label">Overlays</div>
                </div>
            </div>

            <div class="ai-stat-card">
                <div class="ai-stat-icon ai-stat-icon-purple">
                    <span class="dashicons dashicons-media-text"></span>
                </div>
                <div class="ai-stat-content">
                    <div class="ai-stat-number"><?php echo $log_stats['total_logs']; ?></div>
                    <div class="ai-stat-label">Log Entries</div>
                </div>
            </div>

            <div class="ai-stat-card">
                <div class="ai-stat-icon <?php echo !empty($settings['api_token']) ? 'ai-stat-icon-success' : 'ai-stat-icon-warning'; ?>">
                    <span class="dashicons <?php echo !empty($settings['api_token']) ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                </div>
                <div class="ai-stat-content">
                    <div class="ai-stat-number"><?php echo !empty($settings['api_token']) ? 'Active' : 'Setup'; ?></div>
                    <div class="ai-stat-label">API Status</div>
                </div>
            </div>
        </div>
    </div>

        <!-- Tab Content Areas -->
        <div class="ai-tab-content">
            <!-- Dashboard Tab -->
            <div id="dashboard-tab" class="ai-tab-panel active">
                <div class="ai-dashboard-grid">
                    <!-- Recent Activity -->
                    <div class="ai-card">
                        <div class="ai-card-header">
                            <h3>Recent Activity</h3>
                            <button class="ai-btn ai-btn-ghost ai-btn-sm" onclick="switchTab('logs')">View All</button>
                        </div>
                        <div class="ai-card-content">
                            <div id="recent-activity-list" class="ai-activity-list">
                                <div class="ai-loading">Loading recent activity...</div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="ai-card">
                        <div class="ai-card-header">
                            <h3>Storage Overview</h3>
                        </div>
                        <div class="ai-card-content">
                            <div class="ai-storage-stats">
                                <div class="ai-storage-item">
                                    <span class="ai-storage-label">Total Size</span>
                                    <span class="ai-storage-value"><?php echo $gallery_stats['total_size_formatted']; ?></span>
                                </div>
                                <div class="ai-storage-item">
                                    <span class="ai-storage-label">Average Size</span>
                                    <span class="ai-storage-value"><?php echo size_format($gallery_stats['average_size']); ?></span>
                                </div>
                                <div class="ai-storage-item">
                                    <span class="ai-storage-label">Newest Image</span>
                                    <span class="ai-storage-value"><?php echo $gallery_stats['newest_image'] ?: 'None'; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- API Status -->
                    <div class="ai-card">
                        <div class="ai-card-header">
                            <h3>API Configuration</h3>
                            <button class="ai-btn ai-btn-ghost ai-btn-sm" onclick="switchTab('settings')">Configure</button>
                        </div>
                        <div class="ai-card-content">
                            <div class="ai-api-status">
                                <div class="ai-api-item">
                                    <div class="ai-api-info">
                                        <span class="ai-api-name">Replicate API</span>
                                        <span class="ai-api-status-badge <?php echo !empty($settings['api_token']) ? 'ai-status-active' : 'ai-status-inactive'; ?>">
                                            <?php echo !empty($settings['api_token']) ? 'Connected' : 'Not Configured'; ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="ai-api-item">
                                    <div class="ai-api-info">
                                        <span class="ai-api-name">OpenRouter API</span>
                                        <span class="ai-api-status-badge <?php echo !empty($settings['openrouter_api_key']) ? 'ai-status-active' : 'ai-status-inactive'; ?>">
                                            <?php echo !empty($settings['openrouter_api_key']) ? 'Connected' : 'Not Configured'; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gallery Tab -->
            <div id="gallery-tab" class="ai-tab-panel">
                <div class="ai-tab-header">
                    <div class="ai-tab-title">
                        <h2>AI Generated Images</h2>
                        <p>Browse and manage your AI-generated architectural visualizations</p>
                    </div>
                    <div class="ai-tab-actions">
                        <div class="ai-search-box">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"/>
                                <path d="m21 21-4.35-4.35"/>
                            </svg>
                            <input type="text" id="gallery-search" placeholder="Search images..." class="ai-search-input">
                        </div>
                        <select id="gallery-sort" class="ai-select">
                            <option value="date-desc">Newest First</option>
                            <option value="date-asc">Oldest First</option>
                            <option value="name-asc">Name A-Z</option>
                            <option value="name-desc">Name Z-A</option>
                            <option value="size-desc">Largest First</option>
                            <option value="size-asc">Smallest First</option>
                        </select>
                    </div>
                </div>

                <div class="ai-gallery-container">
                    <div id="gallery-grid" class="ai-gallery-grid">
                        <div class="ai-loading">Loading gallery...</div>
                    </div>
                    <div id="gallery-pagination" class="ai-pagination"></div>
                </div>
            </div>

            <!-- Overlays Tab -->
            <div id="overlays-tab" class="ai-tab-panel">
                <div class="ai-tab-header">
                    <div class="ai-tab-title">
                        <h2>Overlay Library</h2>
                        <p>Manage your architectural overlay templates</p>
                    </div>
                    <div class="ai-tab-actions">
                        <button type="button" id="add-overlay-btn" class="ai-btn ai-btn-primary">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 5v14M5 12h14"/>
                            </svg>
                            Add New Overlay
                        </button>
                        <button type="button" id="media-library-btn" class="ai-btn ai-btn-secondary">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                <path d="m9 9 5 12 1.774-3.326a.5.5 0 0 1 .852-.174l2.853 3.5"/>
                                <circle cx="7.5" cy="7.5" r="1.5"/>
                            </svg>
                            From Media Library
                        </button>
                    </div>
                </div>

                <div class="ai-overlays-container">
                    <div id="overlays-grid" class="ai-overlays-grid">
                        <?php if (empty($overlays)): ?>
                            <div class="ai-empty-state">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                    <circle cx="8.5" cy="8.5" r="1.5"/>
                                    <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                                </svg>
                                <h3>No overlays yet</h3>
                                <p>Add your first architectural overlay to get started</p>
                                <button type="button" class="ai-btn ai-btn-primary ai-empty-action">
                                    Add First Overlay
                                </button>
                            </div>
                        <?php else: ?>
                            <?php foreach ($overlays as $overlay): ?>
                                <div class="ai-overlay-card" data-id="<?php echo esc_attr($overlay->id); ?>">
                                    <div class="ai-overlay-image">
                                        <img src="<?php echo esc_url($overlay->image_url); ?>"
                                             alt="<?php echo esc_attr($overlay->title); ?>"
                                             loading="lazy">
                                    </div>
                                    <div class="ai-overlay-content">
                                        <h4><?php echo esc_html($overlay->title); ?></h4>
                                        <p class="ai-overlay-category"><?php echo esc_html(ucfirst(str_replace('_', ' ', $overlay->category))); ?></p>
                                        <div class="ai-overlay-stats">
                                            <span class="ai-usage-count"><?php echo $overlay->usage_count; ?> uses</span>
                                        </div>
                                    </div>
                                    <div class="ai-overlay-actions">
                                        <button type="button" class="ai-btn ai-btn-ghost ai-btn-sm ai-edit-overlay"
                                                data-id="<?php echo esc_attr($overlay->id); ?>">
                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                            </svg>
                                            Edit
                                        </button>
                                        <button type="button" class="ai-btn ai-btn-ghost ai-btn-sm ai-btn-danger ai-delete-overlay"
                                                data-id="<?php echo esc_attr($overlay->id); ?>">
                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <polyline points="3,6 5,6 21,6"/>
                                                <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                                            </svg>
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Logs Tab -->
            <div id="logs-tab" class="ai-tab-panel">
                <div class="ai-tab-header">
                    <div class="ai-tab-title">
                        <h2>Activity Logs</h2>
                        <p>Monitor system events, API requests, and user actions</p>
                    </div>
                    <div class="ai-tab-actions">
                        <div class="ai-logs-filters">
                            <select id="log-category-filter" class="ai-select">
                                <option value="">All Categories</option>
                                <option value="api">API Requests</option>
                                <option value="user_action">User Actions</option>
                                <option value="system">System Events</option>
                                <option value="error">Errors</option>
                                <option value="image_processing">Image Processing</option>
                            </select>
                            <input type="text" id="log-search" placeholder="Search logs..." class="ai-search-input">
                        </div>
                        <div class="ai-logs-actions">
                            <button type="button" id="download-logs-btn" class="ai-btn ai-btn-secondary">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                                Download
                            </button>
                            <button type="button" id="clear-logs-btn" class="ai-btn ai-btn-danger">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="3,6 5,6 21,6"/>
                                    <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                                </svg>
                                Clear Logs
                            </button>
                        </div>
                    </div>
                </div>

                <div class="ai-logs-container">
                    <div id="logs-list" class="ai-logs-list">
                        <div class="ai-loading">Loading logs...</div>
                    </div>
                    <div id="logs-pagination" class="ai-pagination"></div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div id="settings-tab" class="ai-tab-panel">
                <div class="ai-tab-header">
                    <div class="ai-tab-title">
                        <h2>Settings</h2>
                        <p>Configure API keys and plugin preferences</p>
                    </div>
                </div>

                <form method="post" action="" class="ai-settings-form">
                    <?php wp_nonce_field('ai_styled_settings'); ?>

                    <div class="ai-settings-grid">
                        <!-- API Configuration -->
                        <div class="ai-card">
                            <div class="ai-card-header">
                                <h3>API Configuration</h3>
                                <p>Configure your AI service providers</p>
                            </div>
                            <div class="ai-card-content">
                                <div class="ai-form-group">
                                    <label for="api_token">
                                        <strong>Replicate API Token</strong>
                                        <span class="ai-required">*</span>
                                    </label>
                                    <div class="ai-input-group">
                                        <input type="password" id="api_token" name="api_token"
                                               value="<?php echo esc_attr($settings['api_token']); ?>"
                                               class="ai-input-field" placeholder="r8_...">
                                        <button type="button" class="ai-toggle-password" data-target="api_token">
                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                                <circle cx="12" cy="12" r="3"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <p class="ai-field-help">Get your API token from <a href="https://replicate.com/account/api-tokens" target="_blank">Replicate</a></p>
                                </div>

                                <div class="ai-form-group">
                                    <label for="openrouter_api_key">
                                        <strong>OpenRouter API Key</strong>
                                        <span class="ai-required">*</span>
                                    </label>
                                    <div class="ai-input-group">
                                        <input type="password" id="openrouter_api_key" name="openrouter_api_key"
                                               value="<?php echo esc_attr($settings['openrouter_api_key']); ?>"
                                               class="ai-input-field" placeholder="sk-or-...">
                                        <button type="button" class="ai-toggle-password" data-target="openrouter_api_key">
                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                                <circle cx="12" cy="12" r="3"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <p class="ai-field-help">Get your API key from <a href="https://openrouter.ai/keys" target="_blank">OpenRouter</a></p>
                                    <button type="button" id="test-openrouter-btn" class="ai-btn ai-btn-secondary ai-btn-sm" style="margin-top: 0.5rem;">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M9 12l2 2 4-4"/>
                                            <circle cx="12" cy="12" r="9"/>
                                        </svg>
                                        Test OpenRouter Connection
                                    </button>
                                    <div id="openrouter-test-result" style="margin-top: 0.5rem; display: none;"></div>
                                </div>

                                <!-- Processing mode is now always 'new' - manual mode removed -->
                                <input type="hidden" name="processing_mode" value="new">
                                <div class="ai-form-group">
                                    <div class="ai-info-box">
                                        <h4>🤖 AI-Powered Processing</h4>
                                        <p>This plugin now uses advanced AI analysis to generate optimal prompts for each image. The system automatically:</p>
                                        <ul>
                                            <li>Analyzes your uploaded image using OpenRouter's vision models</li>
                                            <li>Examines the selected architectural overlay</li>
                                            <li>Generates a custom prompt optimized for seamless integration</li>
                                            <li>Sends the optimized prompt to Replicate for final image generation</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Settings -->
                        <div class="ai-card">
                            <div class="ai-card-header">
                                <h3>Advanced Settings</h3>
                                <p>Fine-tune plugin behavior</p>
                            </div>
                            <div class="ai-card-content">
                                <div class="ai-form-group">
                                    <label for="max_file_size">
                                        <strong>Maximum File Size</strong>
                                    </label>
                                    <select id="max_file_size" name="max_file_size" class="ai-input-field">
                                        <option value="5242880" <?php selected($settings['max_file_size'], 5242880); ?>>5 MB</option>
                                        <option value="10485760" <?php selected($settings['max_file_size'], 10485760); ?>>10 MB</option>
                                        <option value="20971520" <?php selected($settings['max_file_size'], 20971520); ?>>20 MB</option>
                                    </select>
                                </div>

                                <div class="ai-form-group">
                                    <label for="rate_limit">
                                        <strong>Rate Limit (requests per hour)</strong>
                                    </label>
                                    <input type="number" id="rate_limit" name="rate_limit"
                                           value="<?php echo esc_attr($settings['rate_limit']); ?>"
                                           class="ai-input-field" min="1" max="1000">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="ai-form-actions">
                        <button type="submit" name="save_settings" class="ai-btn ai-btn-primary">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                                <polyline points="17,21 17,13 7,13 7,21"/>
                                <polyline points="7,3 7,8 15,8"/>
                            </svg>
                            Save Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </main>
</div>

<!-- Modals -->
<div id="upload-modal" class="ai-modal" style="display: none;">
    <div class="ai-modal-overlay"></div>
    <div class="ai-modal-container">
        <div class="ai-modal-header">
            <h3>Add New Overlay</h3>
            <button type="button" id="close-upload-modal" class="ai-close-btn">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"/>
                    <line x1="6" y1="6" x2="18" y2="18"/>
                </svg>
            </button>
        </div>

        <div class="ai-modal-body">
            <form id="overlay-upload-form" enctype="multipart/form-data">
                <div class="ai-upload-methods">
                    <div class="ai-upload-method" id="file-upload-method">
                        <div class="ai-upload-dropzone" id="upload-dropzone">
                            <div class="ai-upload-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                            </div>
                            <h4>Drop PNG file here</h4>
                            <p>or click to browse files</p>
                            <div class="ai-upload-specs">
                                Transparent PNG only • Max <?php echo size_format($settings['max_file_size']); ?>
                            </div>
                        </div>
                        <input type="file" id="overlay-file" name="overlay_image" accept="image/png" style="display: none;">
                        <input type="hidden" id="media-attachment-id" name="media_id" value="">
                    </div>
                </div>

                <div id="image-preview-area" class="ai-image-preview" style="display: none;">
                    <div class="ai-preview-image">
                        <img id="preview-img" src="" alt="Preview">
                    </div>
                    <div class="ai-preview-info">
                        <h4 id="preview-name"></h4>
                        <p id="preview-size"></p>
                        <button type="button" id="change-image" class="ai-btn ai-btn-small">Change Image</button>
                    </div>
                </div>

                <div class="ai-overlay-details">
                    <div class="ai-form-row">
                        <div class="ai-form-group">
                            <label for="overlay-title">
                                <strong>Overlay Name</strong>
                                <span class="ai-required">*</span>
                            </label>
                            <input type="text" id="overlay-title" name="title" required class="ai-input-field" placeholder="e.g., Modern Glass Room">
                        </div>

                        <div class="ai-form-group">
                            <label for="overlay-category">
                                <strong>Category</strong>
                            </label>
                            <select id="overlay-category" name="category" class="ai-input-field">
                                <option value="Glass Room">Glass Room</option>
                                <option value="Veranda">Veranda</option>
                                <option value="Shading System">Shading System</option>
                            </select>
                        </div>
                    </div>

                    <div class="ai-form-group">
                        <label for="overlay-description">
                            <strong>Description</strong>
                        </label>
                        <textarea id="overlay-description" name="description" rows="3" class="ai-input-field" placeholder="Describe this architectural element..."></textarea>
                    </div>

                    <div class="ai-form-group">
                        <label for="overlay-prompt">
                            <strong>Custom AI Prompt</strong>
                            <span class="ai-optional">(Optional)</span>
                        </label>
                        <textarea id="overlay-prompt" name="prompt_template" rows="4" class="ai-input-field" placeholder="Enter a custom prompt for this overlay, or leave empty for automatic generation..."></textarea>
                        <p class="ai-field-help">This prompt will be used when users select this overlay. Leave empty to use the default prompt for this category.</p>
                    </div>
                </div>

                <div class="ai-modal-actions">
                    <button type="button" id="cancel-upload" class="ai-btn ai-btn-secondary">Cancel</button>
                    <button type="submit" id="upload-overlay-btn" class="ai-btn ai-btn-primary">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="17,8 12,3 7,8"/>
                            <line x1="12" y1="3" x2="12" y2="15"/>
                        </svg>
                        Upload Overlay
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Image Lightbox Modal -->
<div id="image-lightbox" class="ai-modal ai-lightbox" style="display: none;">
    <div class="ai-modal-overlay"></div>
    <div class="ai-lightbox-container">
        <button type="button" id="close-lightbox" class="ai-close-btn">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
        </button>
        <div class="ai-lightbox-content">
            <img id="lightbox-image" src="" alt="">
            <div class="ai-lightbox-info">
                <h4 id="lightbox-title"></h4>
                <p id="lightbox-details"></p>
            </div>
        </div>
    </div>
</div>
