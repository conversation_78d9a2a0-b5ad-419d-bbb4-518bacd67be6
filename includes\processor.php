<?php
/**
 * AI Image Processor - Handles all image processing and overlay management
 */

defined('ABSPATH') || exit;

final class AI_Image_Processor {
    private string $api_token;
    private string $model_endpoint;
    private string $base_url = 'https://api.replicate.com/v1';
    private int $max_file_size;
    private array $allowed_formats;
    private string $openrouter_api_key;
    private string $openrouter_model;
    private string $processing_mode;
    private string $openrouter_base_url = 'https://openrouter.ai/api/v1';
    private array $processing_logs = [];

    public function __construct() {
        $this->api_token = get_option('ai_styled_api_token', '');
        $this->model_endpoint = get_option('ai_styled_model_endpoint', 'flux-kontext-apps/multi-image-kontext-max');
        $this->max_file_size = get_option('ai_styled_max_file_size', 10485760);
        $this->allowed_formats = get_option('ai_styled_allowed_formats', ['jpg', 'jpeg', 'png', 'webp']);
        $this->openrouter_api_key = get_option('ai_styled_openrouter_api_key', '');
        $this->openrouter_model = get_option('ai_styled_openrouter_model', 'openai/gpt-4o-mini');
        $this->processing_mode = 'new'; // Always use new mode - manual mode removed

        // Include logger
        require_once AI_STYLED_PATH . 'includes/logger.php';

        // Log configuration on initialization for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            AI_Styled_Logger::log_system_event(
                'processor_initialized',
                'AI Image Processor initialized with configuration',
                [
                    'processing_mode' => $this->processing_mode,
                    'has_replicate_token' => !empty($this->api_token),
                    'has_openrouter_key' => !empty($this->openrouter_api_key),
                    'openrouter_model' => $this->openrouter_model,
                    'replicate_model' => $this->model_endpoint
                ]
            );
        }
    }
    
    /**
     * Process user image with AI overlay
     */
    public function process(array $user_image, int $overlay_id, string $custom_prompt = ''): array {
        $start_time = microtime(true);

        // Initialize logging data collection
        $this->processing_logs = [
            'openrouter' => [
                'status' => 'pending',
                'prompt' => '',
                'time' => 0,
                'model' => $this->openrouter_model
            ],
            'replicate' => [
                'status' => 'pending',
                'prediction_id' => '',
                'time' => 0,
                'model' => $this->model_endpoint
            ]
        ];

        // Log processing start
        AI_Styled_Logger::log_image_processing(
            'processing_started',
            "Image processing started for overlay ID: {$overlay_id}",
            [
                'overlay_id' => $overlay_id,
                'user_image_size' => $user_image['size'] ?? 0,
                'custom_prompt' => !empty($custom_prompt),
                'processing_mode' => $this->processing_mode
            ]
        );

        // Validate inputs
        if (!$this->validate_image($user_image)) {
            AI_Styled_Logger::log_error('Image validation failed', [
                'file_size' => $user_image['size'] ?? 0,
                'file_type' => $user_image['type'] ?? 'unknown'
            ]);
            return $this->error('Invalid image file. Please check format and size.');
        }

        if (!$this->check_rate_limit()) {
            AI_Styled_Logger::log_user_action('rate_limit_exceeded', 'User exceeded rate limit');
            return $this->error('Rate limit exceeded. Please try again later.');
        }

        $overlay = $this->get_overlay($overlay_id);
        if (!$overlay) {
            AI_Styled_Logger::log_error('Overlay not found', ['overlay_id' => $overlay_id]);
            return $this->error('Overlay not found.');
        }

        // If no API token, return test mode
        if (empty($this->api_token)) {
            AI_Styled_Logger::log_system_event('test_mode', 'Processing in test mode - no API token configured');
            return $this->test_mode_response($user_image, $overlay);
        }
        
        try {
            // Generate prompt based on processing mode BEFORE uploading temp image
            // This ensures the original user_image array with tmp_name is available for OpenRouter
            $prompt = $this->generate_prompt($overlay, $custom_prompt, $user_image);
            if (!$prompt) {
                return $this->error('Failed to generate prompt.');
            }

            // Upload user image temporarily after prompt generation
            $user_image_url = $this->upload_temp_image($user_image);
            if (!$user_image_url) {
                return $this->error('Failed to upload image.');
            }

            // Create prediction
            $prediction = $this->create_prediction($user_image_url, $overlay->image_url, $prompt);
            if (!$prediction) {
                $this->cleanup_temp_file($user_image_url);
                return $this->error('Failed to start AI processing.');
            }

            // Wait for completion
            $result = $this->wait_for_completion($prediction['id']);
            $this->cleanup_temp_file($user_image_url);

            if ($result['success']) {
                $this->update_usage_count($overlay_id);
                return $this->success($result['data']);
            }

            return $this->error($result['message']);

        } catch (Exception $e) {
            error_log('AI Image Processor Error: ' . $e->getMessage());
            return $this->error('Processing failed. Please try again.');
        }
    }
    
    /**
     * Upload overlay image
     */
    public function upload_overlay(?array $image_file, array $form_data): array {
        if (!$image_file || !$this->validate_overlay_image($image_file)) {
            return $this->error('Invalid overlay image. Please upload a PNG file.');
        }
        
        // Process upload
        $upload_result = $this->process_overlay_upload($image_file);
        if (!$upload_result['success']) {
            return $upload_result;
        }
        
        // Save to database
        $overlay_data = [
            'title' => sanitize_text_field($form_data['title'] ?? ''),
            'description' => sanitize_textarea_field($form_data['description'] ?? ''),
            'image_url' => $upload_result['url'],
            'category' => sanitize_text_field($form_data['category'] ?? 'general'),
            'prompt_template' => sanitize_textarea_field($form_data['prompt_template'] ?? ''),
            'created_at' => current_time('mysql')
        ];
        
        global $wpdb;
        $result = $wpdb->insert("{$wpdb->prefix}ai_overlays", $overlay_data);
        
        if ($result === false) {
            // Clean up uploaded file on database error
            $this->delete_uploaded_file($upload_result['url']);
            return $this->error('Failed to save overlay to database.');
        }
        
        return $this->success([
            'message' => 'Overlay uploaded successfully!',
            'overlay_id' => $wpdb->insert_id
        ]);
    }
    
    /**
     * Delete overlay
     */
    public function delete_overlay(int $overlay_id): array {
        if ($overlay_id <= 0) {
            return $this->error('Invalid overlay ID.');
        }
        
        global $wpdb;
        
        // Get overlay data before deletion
        $overlay = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$wpdb->prefix}ai_overlays WHERE id = %d", $overlay_id)
        );
        
        if (!$overlay) {
            return $this->error('Overlay not found.');
        }
        
        // Delete from database
        $deleted = $wpdb->delete(
            "{$wpdb->prefix}ai_overlays",
            ['id' => $overlay_id],
            ['%d']
        );
        
        if ($deleted === false) {
            return $this->error('Failed to delete overlay.');
        }
        
        // Clean up file
        $this->delete_uploaded_file($overlay->image_url);
        
        return $this->success(['message' => 'Overlay deleted successfully!']);
    }
    
    /**
     * Get all overlays
     */
    public function get_overlays(): array {
        global $wpdb;
        return $wpdb->get_results(
            "SELECT * FROM {$wpdb->prefix}ai_overlays ORDER BY usage_count DESC, created_at DESC"
        );
    }
    
    /**
     * Get single overlay
     */
    private function get_overlay(int $overlay_id): ?object {
        global $wpdb;
        return $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$wpdb->prefix}ai_overlays WHERE id = %d", $overlay_id)
        );
    }
    
    /**
     * Validate user image
     */
    private function validate_image(array $image): bool {
        if (!isset($image['tmp_name']) || !is_uploaded_file($image['tmp_name'])) {
            return false;
        }
        
        $file_info = wp_check_filetype_and_ext($image['tmp_name'], $image['name']);
        $file_type = strtolower(pathinfo($image['name'], PATHINFO_EXTENSION));
        
        if (!in_array($file_type, $this->allowed_formats)) {
            return false;
        }
        
        if ($image['size'] > $this->max_file_size) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate overlay image
     */
    private function validate_overlay_image(array $image): bool {
        if (!isset($image['tmp_name']) || !is_uploaded_file($image['tmp_name'])) {
            return false;
        }
        
        $file_info = wp_check_filetype_and_ext($image['tmp_name'], $image['name']);
        
        // Allow PNG for overlays (transparency support)
        if ($file_info['type'] !== 'image/png') {
            return false;
        }
        
        if ($image['size'] > $this->max_file_size) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check rate limiting
     */
    private function check_rate_limit(): bool {
        $rate_limit = get_option('ai_styled_rate_limit', 50);
        $user_ip = $_SERVER['REMOTE_ADDR'] ?? '';
        $transient_key = 'ai_styled_rate_' . md5($user_ip);
        
        $requests = get_transient($transient_key) ?: 0;
        
        if ($requests >= $rate_limit) {
            return false;
        }
        
        set_transient($transient_key, $requests + 1, HOUR_IN_SECONDS);
        return true;
    }
    
    /**
     * Upload temporary image
     */
    private function upload_temp_image(array $image): ?string {
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/ai-temp/';
        
        if (!wp_mkdir_p($temp_dir)) {
            return null;
        }
        
        $filename = 'temp_' . uniqid() . '_' . sanitize_file_name($image['name']);
        $temp_path = $temp_dir . $filename;
        
        if (move_uploaded_file($image['tmp_name'], $temp_path)) {
            return $upload_dir['baseurl'] . '/ai-temp/' . $filename;
        }
        
        return null;
    }
    
    /**
     * Process overlay upload
     */
    private function process_overlay_upload(array $image): array {
        $upload_dir = wp_upload_dir();
        $ai_dir = $upload_dir['basedir'] . '/ai-overlays/';
        
        if (!wp_mkdir_p($ai_dir)) {
            return $this->error('Failed to create upload directory.');
        }
        
        $filename = 'overlay_' . uniqid() . '_' . sanitize_file_name($image['name']);
        $file_path = $ai_dir . $filename;
        
        if (move_uploaded_file($image['tmp_name'], $file_path)) {
            return [
                'success' => true,
                'url' => $upload_dir['baseurl'] . '/ai-overlays/' . $filename,
                'path' => $file_path
            ];
        }
        
        return $this->error('Failed to upload file.');
    }
    
    /**
     * Generate AI prompt
     */
    private function generate_prompt(object $overlay, string $custom_prompt, ?array $user_image = null): ?string {
        // If custom prompt is provided, use it
        if (!empty(trim($custom_prompt))) {
            return trim($custom_prompt);
        }

        // Check processing mode
        AI_Styled_Logger::log_image_processing(
            'prompt_generation_mode_check',
            "Processing mode: {$this->processing_mode}, OpenRouter API key configured: " . (!empty($this->openrouter_api_key) ? 'Yes' : 'No'),
            [
                'processing_mode' => $this->processing_mode,
                'has_openrouter_key' => !empty($this->openrouter_api_key),
                'has_user_image' => $user_image !== null
            ]
        );

        // Force OpenRouter usage for debugging - remove this later
        AI_Styled_Logger::log_image_processing(
            'openrouter_condition_check',
            'Checking OpenRouter conditions',
            [
                'processing_mode' => $this->processing_mode,
                'processing_mode_is_new' => $this->processing_mode === 'new',
                'has_user_image' => $user_image !== null,
                'has_openrouter_key' => !empty($this->openrouter_api_key),
                'openrouter_key_length' => strlen($this->openrouter_api_key ?? ''),
                'will_use_openrouter' => ($this->processing_mode === 'new' && $user_image && !empty($this->openrouter_api_key))
            ]
        );

        if ($this->processing_mode === 'new' && $user_image && !empty($this->openrouter_api_key)) {
            // New mode: Use OpenRouter to generate prompt
            $user_image_path = $user_image['tmp_name'];

            // Verify the user image file exists before proceeding
            if (!file_exists($user_image_path)) {
                AI_Styled_Logger::log_error('User image tmp_name file does not exist for OpenRouter processing', [
                    'tmp_name' => $user_image_path,
                    'user_image_keys' => array_keys($user_image)
                ]);
                // Fallback to current mode
            } else {
                AI_Styled_Logger::log_image_processing(
                'openrouter_download_overlay_start',
                "Downloading overlay for analysis: {$overlay->image_url}",
                ['overlay_url' => $overlay->image_url]
            );

            // Download overlay image temporarily for analysis
            $overlay_temp_path = $this->download_overlay_for_analysis($overlay->image_url);
            if ($overlay_temp_path) {
                AI_Styled_Logger::log_image_processing(
                    'openrouter_overlay_downloaded',
                    "Overlay downloaded successfully: {$overlay_temp_path}",
                    ['temp_path' => $overlay_temp_path]
                );

                $generated_prompt = $this->generate_prompt_with_openrouter($user_image_path, $overlay_temp_path, $overlay);

                // Clean up temporary overlay file
                wp_delete_file($overlay_temp_path);

                if ($generated_prompt) {
                    AI_Styled_Logger::log_image_processing(
                        'openrouter_prompt_success',
                        "OpenRouter generated prompt successfully",
                        ['prompt_length' => strlen($generated_prompt)]
                    );
                    return $generated_prompt;
                }
            } else {
                AI_Styled_Logger::log_error('Failed to download overlay for OpenRouter analysis', [
                    'overlay_url' => $overlay->image_url
                ]);
            }

                // Fallback to current mode if OpenRouter fails
                AI_Styled_Logger::log_error('OpenRouter prompt generation failed, falling back to current mode', [
                    'processing_mode' => $this->processing_mode,
                    'has_openrouter_key' => !empty($this->openrouter_api_key),
                    'overlay_temp_path' => $overlay_temp_path ?? 'null'
                ]);
            }
        }

        // Current mode: Use predefined prompts
        if (!empty($overlay->prompt_template)) {
            return $overlay->prompt_template;
        }

        // Default prompt based on category
        $prompts = [
            'Glass Room' => 'Integrate a sleek, modern glass room into the existing garden environment in a way that looks architecturally intentional and visually seamless. Place the glass room in the left corner of the garden, adjacent to the side fence and facing the main patio, ensuring no disruption to the original layout. Keep all existing elements intact—such as the house structure, landscaping, fence, lawn, trees, and any garden furniture. The glass room should harmonize with the overall style of the house and garden, using realistic reflections that mirror nearby plants and structures. Match the lighting angle, brightness, and color tone precisely with the original photo. Ensure the structure casts natural, consistent shadows and feels proportionate to the surrounding space. The result should resemble a professionally planned extension that fits naturally into the existing scene.',
            'Veranda' => 'Seamlessly integrate the modern veranda structure into the existing garden scene without altering any original elements of the base image. Position the veranda attached to the back wall of the house, extending over the existing patio area, while preserving all existing features including: house architecture, fence design, lawn areas, existing plants, patio elements, and garden furniture. The veranda should appear as a natural extension that complements the space. Match the lighting conditions, shadow direction, and color temperature of the original garden photo. Ensure the veranda\'s glass and metal surfaces reflect the surrounding garden environment accurately. Maintain the exact proportions and design details of both the original garden layout and the veranda structure. The integration should look professionally planned and architecturally coherent.',
            'Shading System' => 'Seamlessly integrate a modern shading system into the existing garden scene without altering any original elements of the base image. Position the shading system over the seating area on the wooden deck near the right-side fence, while preserving all existing features including: house architecture, fence design, lawn areas, existing plants, patio elements, and garden furniture. The shading system should appear as a functional and stylish addition that enhances comfort and complements the outdoor space. Match the lighting conditions, shadow direction, and color temperature of the original garden photo. Ensure the shading system casts realistic shadows and interacts naturally with surrounding structures. Maintain the exact proportions and visual coherence between the shading system and the garden layout. The integration should look professionally installed and seamlessly blended.'
        ];

        return $prompts[$overlay->category] ?? $prompts['Glass Room'];
    }

    /**
     * Download overlay image for analysis
     */
    private function download_overlay_for_analysis(string $overlay_url): ?string {
        AI_Styled_Logger::log_image_processing(
            'download_overlay_start',
            "Starting overlay download for analysis",
            ['overlay_url' => $overlay_url]
        );

        $response = wp_remote_get($overlay_url, ['timeout' => 30]);

        if (is_wp_error($response)) {
            AI_Styled_Logger::log_error('Failed to download overlay image', [
                'overlay_url' => $overlay_url,
                'error' => $response->get_error_message()
            ]);
            return null;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            AI_Styled_Logger::log_error('Overlay download returned non-200 status', [
                'overlay_url' => $overlay_url,
                'response_code' => $response_code
            ]);
            return null;
        }

        $image_data = wp_remote_retrieve_body($response);
        if (empty($image_data)) {
            AI_Styled_Logger::log_error('Overlay download returned empty data', [
                'overlay_url' => $overlay_url
            ]);
            return null;
        }

        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/ai-temp/';

        if (!wp_mkdir_p($temp_dir)) {
            AI_Styled_Logger::log_error('Failed to create temp directory for overlay analysis', [
                'temp_dir' => $temp_dir
            ]);
            return null;
        }

        $temp_filename = 'overlay_analysis_' . uniqid() . '.png';
        $temp_path = $temp_dir . $temp_filename;

        if (file_put_contents($temp_path, $image_data)) {
            AI_Styled_Logger::log_image_processing(
                'download_overlay_success',
                "Overlay downloaded successfully for analysis",
                [
                    'overlay_url' => $overlay_url,
                    'temp_path' => $temp_path,
                    'file_size' => strlen($image_data)
                ]
            );
            return $temp_path;
        }

        AI_Styled_Logger::log_error('Failed to save overlay to temp file', [
            'overlay_url' => $overlay_url,
            'temp_path' => $temp_path
        ]);

        return null;
    }
    
    /**
     * Create Replicate prediction
     */
    private function create_prediction(string $user_image_url, string $overlay_url, string $prompt): ?array {
        $this->processing_logs['replicate']['status'] = 'processing';

        AI_Styled_Logger::log_image_processing(
            'replicate_prediction_started',
            'Starting Replicate prediction',
            [
                'model_endpoint' => $this->model_endpoint,
                'prompt' => $prompt,
                'user_image_url' => $user_image_url,
                'overlay_url' => $overlay_url
            ]
        );

        $data = [
            'input' => [
                'prompt' => $prompt,
                'aspect_ratio' => '1:1',
                'input_image_1' => $user_image_url,
                'input_image_2' => $overlay_url
            ]
        ];

        // Store request data in logs
        $this->processing_logs['replicate']['request_data'] = $data;
        $this->processing_logs['replicate']['full_request'] = [
            'url' => $this->base_url . "/models/{$this->model_endpoint}/predictions",
            'method' => 'POST',
            'headers' => [
                'Authorization' => 'Bearer [REDACTED]',
                'Content-Type' => 'application/json',
                'Prefer' => 'wait'
            ],
            'body' => $data
        ];

        $response = $this->make_api_request('POST', "/models/{$this->model_endpoint}/predictions", $data);

        if ($response && isset($response['id'])) {
            $this->processing_logs['replicate']['prediction_id'] = $response['id'];

            AI_Styled_Logger::log_image_processing(
                'replicate_prediction_created',
                'Replicate prediction created successfully',
                [
                    'prediction_id' => $response['id'],
                    'status' => $response['status'] ?? 'unknown'
                ]
            );
            return $response;
        }

        $this->processing_logs['replicate']['status'] = 'error';

        AI_Styled_Logger::log_error('Failed to create Replicate prediction', [
            'response' => $response,
            'prompt' => $prompt
        ]);

        return null;
    }
    
    /**
     * Wait for prediction completion
     */
    private function wait_for_completion(string $prediction_id, int $max_wait = 300): array {
        $start_time = time();
        $replicate_start_time = microtime(true);
        
        while (time() - $start_time < $max_wait) {
            $status = $this->make_api_request('GET', "/predictions/{$prediction_id}");
            
            if (!$status) {
                return $this->error('Failed to check processing status.');
            }
            
            if ($status['status'] === 'succeeded') {
                $image_url = is_array($status['output']) ? $status['output'][0] : $status['output'];
                $attachment_id = $this->save_result_image($image_url);

                if ($attachment_id) {
                    $processing_time = time() - $start_time;
                    $replicate_time = microtime(true) - $replicate_start_time;

                    // Update logging data with full response details
                    $this->processing_logs['replicate']['status'] = 'success';
                    $this->processing_logs['replicate']['time'] = round($replicate_time, 2);
                    $this->processing_logs['replicate']['final_status'] = $status;
                    $this->processing_logs['replicate']['output_url'] = $image_url;
                    $this->processing_logs['replicate']['full_response'] = $status;

                    // Log successful processing
                    AI_Styled_Logger::log_image_processing(
                        'processing_completed',
                        "Image processing completed successfully",
                        [
                            'attachment_id' => $attachment_id,
                            'processing_time' => $processing_time,
                            'result_url' => wp_get_attachment_url($attachment_id),
                            'replicate_time' => $replicate_time
                        ]
                    );

                    return [
                        'success' => true,
                        'data' => [
                            'image_url' => wp_get_attachment_url($attachment_id),
                            'attachment_id' => $attachment_id,
                            'processing_time' => $processing_time
                        ]
                    ];
                }

                AI_Styled_Logger::log_error('Failed to save result image', ['image_url' => $image_url]);
                return $this->error('Failed to save result image.');
            }

            if ($status['status'] === 'failed') {
                $replicate_time = microtime(true) - $replicate_start_time;

                // Update logging data for failure
                $this->processing_logs['replicate']['status'] = 'error';
                $this->processing_logs['replicate']['time'] = round($replicate_time, 2);
                $this->processing_logs['replicate']['error_response'] = $status;
                $this->processing_logs['replicate']['full_error_response'] = $status;

                AI_Styled_Logger::log_error('AI processing failed', ['error' => $status['error'] ?? 'Unknown error']);
                return $this->error($status['error'] ?? 'AI processing failed.');
            }
            
            sleep(2);
        }
        
        return $this->error('Processing timeout. Please try again.');
    }
    
    /**
     * Make API request
     */
    private function make_api_request(string $method, string $endpoint, ?array $data = null): ?array {
        $start_time = microtime(true);
        $url = $this->base_url . $endpoint;

        $args = [
            'method' => $method,
            'headers' => [
                'Authorization' => 'Bearer ' . $this->api_token,
                'Content-Type' => 'application/json',
                'Prefer' => 'wait'
            ],
            'timeout' => 30
        ];

        if ($data && $method !== 'GET') {
            $args['body'] = wp_json_encode($data);
        }

        $response = wp_remote_request($url, $args);
        $execution_time = microtime(true) - $start_time;

        if (is_wp_error($response)) {
            AI_Styled_Logger::log_api_request(
                'Replicate',
                $endpoint,
                $data ?: [],
                null,
                $execution_time
            );
            error_log('API Request Error: ' . $response->get_error_message());
            return null;
        }

        $code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $response_data = json_decode($body, true);

        // Log API request
        AI_Styled_Logger::log_api_request(
            'Replicate',
            $endpoint,
            $data ?: [],
            $response_data,
            $execution_time
        );

        if ($code >= 200 && $code < 300) {
            return $response_data;
        }

        error_log("API Error {$code}: {$body}");
        return null;
    }

    /**
     * Make OpenRouter API request
     */
    private function make_openrouter_request(string $method, string $endpoint, ?array $data = null): ?array {
        $start_time = microtime(true);
        $url = $this->openrouter_base_url . $endpoint;

        $args = [
            'method' => $method,
            'headers' => [
                'Authorization' => 'Bearer ' . $this->openrouter_api_key,
                'Content-Type' => 'application/json',
                'HTTP-Referer' => home_url(),
                'X-Title' => 'AI Styled Image Plugin'
            ],
            'timeout' => 60
        ];

        if ($data && $method !== 'GET') {
            $args['body'] = wp_json_encode($data);

            // Log request details for debugging
            AI_Styled_Logger::log_image_processing(
                'openrouter_request_prepared',
                'OpenRouter request prepared',
                [
                    'url' => $url,
                    'method' => $method,
                    'body_size' => strlen($args['body']),
                    'model' => $data['model'] ?? 'unknown',
                    'messages_count' => count($data['messages'] ?? []),
                    'full_request_data' => $data,
                    'request_headers' => $args['headers']
                ]
            );
        }

        $response = wp_remote_request($url, $args);
        $execution_time = microtime(true) - $start_time;

        if (is_wp_error($response)) {
            AI_Styled_Logger::log_api_request(
                'OpenRouter',
                $endpoint,
                $data ?: [],
                null,
                $execution_time
            );
            AI_Styled_Logger::log_error('OpenRouter API Request Error', [
                'endpoint' => $endpoint,
                'error' => $response->get_error_message(),
                'execution_time' => $execution_time
            ]);
            return null;
        }

        $code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $response_data = json_decode($body, true);

        // Log API request
        AI_Styled_Logger::log_api_request(
            'OpenRouter',
            $endpoint,
            $data ?: [],
            $response_data,
            $execution_time
        );

        if ($code >= 200 && $code < 300) {
            AI_Styled_Logger::log_image_processing(
                'openrouter_api_success',
                "OpenRouter API request successful",
                [
                    'endpoint' => $endpoint,
                    'response_code' => $code,
                    'execution_time' => $execution_time
                ]
            );
            return $response_data;
        }

        AI_Styled_Logger::log_error("OpenRouter API Error", [
            'endpoint' => $endpoint,
            'response_code' => $code,
            'response_body' => $body,
            'response_headers' => wp_remote_retrieve_headers($response),
            'execution_time' => $execution_time,
            'request_url' => $url,
            'request_method' => $method,
            'model' => $data['model'] ?? 'unknown'
        ]);
        return null;
    }

    /**
     * Convert image to base64 for OpenRouter
     */
    private function image_to_base64(string $image_path): ?string {
        if (!file_exists($image_path)) {
            AI_Styled_Logger::log_error('Image file does not exist for base64 conversion', [
                'image_path' => $image_path
            ]);
            return null;
        }

        $image_data = file_get_contents($image_path);
        if ($image_data === false) {
            AI_Styled_Logger::log_error('Failed to read image file for base64 conversion', [
                'image_path' => $image_path
            ]);
            return null;
        }

        $mime_type = mime_content_type($image_path);
        if (!$mime_type) {
            AI_Styled_Logger::log_error('Failed to determine MIME type for image', [
                'image_path' => $image_path
            ]);
            return null;
        }

        $base64_data = 'data:' . $mime_type . ';base64,' . base64_encode($image_data);

        AI_Styled_Logger::log_image_processing(
            'image_base64_conversion_success',
            "Image converted to base64 successfully",
            [
                'image_path' => $image_path,
                'mime_type' => $mime_type,
                'data_size' => strlen($image_data),
                'base64_size' => strlen($base64_data)
            ]
        );

        return $base64_data;
    }

    /**
     * Generate prompt using OpenRouter GPT-4.1 Vision
     */
    private function generate_prompt_with_openrouter(string $user_image_path, string $overlay_image_path, object $overlay): ?string {
        $openrouter_start_time = microtime(true);

        if (empty($this->openrouter_api_key)) {
            AI_Styled_Logger::log_error('OpenRouter API key not configured');
            $this->processing_logs['openrouter']['status'] = 'error';
            return null;
        }

        $this->processing_logs['openrouter']['status'] = 'processing';

        AI_Styled_Logger::log_image_processing(
            'openrouter_prompt_generation_started',
            'Starting OpenRouter prompt generation',
            [
                'overlay_id' => $overlay->id,
                'overlay_title' => $overlay->title,
                'model' => $this->openrouter_model
            ]
        );

        // Test OpenRouter API connectivity first
        $test_data = [
            'model' => $this->openrouter_model,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => 'Test message - respond with "API working"'
                ]
            ],
            'max_tokens' => 10
        ];

        $test_response = $this->make_openrouter_request('POST', '/chat/completions', $test_data);

        if (!$test_response || !isset($test_response['choices'][0]['message']['content'])) {
            AI_Styled_Logger::log_error('OpenRouter API test failed before image analysis', [
                'test_response' => $test_response,
                'model' => $this->openrouter_model
            ]);
            $this->processing_logs['openrouter']['status'] = 'error';
            return null;
        }

        AI_Styled_Logger::log_image_processing(
            'openrouter_api_test_success',
            'OpenRouter API test successful',
            [
                'test_response' => $test_response['choices'][0]['message']['content']
            ]
        );

        // Convert images to base64
        $user_image_base64 = $this->image_to_base64($user_image_path);
        $overlay_image_base64 = $this->image_to_base64($overlay_image_path);

        if (!$user_image_base64 || !$overlay_image_base64) {
            AI_Styled_Logger::log_error('Failed to convert images to base64 for OpenRouter');
            return null;
        }

        $system_prompt = "You are an AI that creates image generation prompts. Analyze the two images and create a single detailed prompt for seamlessly integrating the architectural element from the second image into the first image. Output only the prompt, no other text.";

        $user_prompt = "Create a prompt to integrate the {$overlay->title} from the second image into the first image. Make it look natural and realistic.";

        $data = [
            'model' => $this->openrouter_model,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => $system_prompt
                ],
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => $user_prompt
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => $user_image_base64
                            ]
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => $overlay_image_base64
                            ]
                        ]
                    ]
                ]
            ],
            'max_tokens' => 800,
            'temperature' => 0.7
        ];

        $response = $this->make_openrouter_request('POST', '/chat/completions', $data);

        if ($response && isset($response['choices'][0]['message']['content'])) {
            $generated_prompt = trim($response['choices'][0]['message']['content']);
            $openrouter_time = microtime(true) - $openrouter_start_time;

            // Update logging data with full response details
            $this->processing_logs['openrouter']['status'] = 'success';
            $this->processing_logs['openrouter']['prompt'] = $generated_prompt;
            $this->processing_logs['openrouter']['time'] = round($openrouter_time, 2);
            $this->processing_logs['openrouter']['response'] = $response;
            $this->processing_logs['openrouter']['tokens_used'] = $response['usage'] ?? null;
            $this->processing_logs['openrouter']['request_data'] = $data;
            $this->processing_logs['openrouter']['full_request'] = [
                'url' => $this->openrouter_base_url . '/chat/completions',
                'method' => 'POST',
                'headers' => [
                    'Authorization' => 'Bearer [REDACTED]',
                    'Content-Type' => 'application/json',
                    'HTTP-Referer' => home_url(),
                    'X-Title' => 'AI Styled Image Plugin'
                ],
                'body' => $data
            ];

            AI_Styled_Logger::log_image_processing(
                'openrouter_prompt_generated',
                'OpenRouter successfully generated prompt',
                [
                    'overlay_id' => $overlay->id,
                    'prompt_length' => strlen($generated_prompt),
                    'generated_prompt' => $generated_prompt,
                    'execution_time' => $openrouter_time
                ]
            );

            return $generated_prompt;
        }

        $openrouter_time = microtime(true) - $openrouter_start_time;
        $this->processing_logs['openrouter']['status'] = 'error';
        $this->processing_logs['openrouter']['time'] = round($openrouter_time, 2);
        $this->processing_logs['openrouter']['error_response'] = $response;
        $this->processing_logs['openrouter']['request_data'] = $data ?? null;
        $this->processing_logs['openrouter']['full_request'] = [
            'url' => $this->openrouter_base_url . '/chat/completions',
            'method' => 'POST',
            'headers' => [
                'Authorization' => 'Bearer [REDACTED]',
                'Content-Type' => 'application/json',
                'HTTP-Referer' => home_url(),
                'X-Title' => 'AI Styled Image Plugin'
            ],
            'body' => $data ?? null
        ];

        AI_Styled_Logger::log_error('OpenRouter failed to generate prompt', [
            'response' => $response,
            'overlay_id' => $overlay->id,
            'execution_time' => $openrouter_time
        ]);

        return null;
    }

    /**
     * Test OpenRouter connectivity
     */
    public function test_openrouter_connection(): array {
        if (empty($this->openrouter_api_key)) {
            return [
                'success' => false,
                'message' => 'OpenRouter API key not configured'
            ];
        }

        // Simple test request to OpenRouter
        $test_data = [
            'model' => $this->openrouter_model,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => 'Hello, this is a test message. Please respond with "OpenRouter connection successful".'
                ]
            ],
            'max_tokens' => 50
        ];

        $response = $this->make_openrouter_request('POST', '/chat/completions', $test_data);

        if ($response && isset($response['choices'][0]['message']['content'])) {
            return [
                'success' => true,
                'message' => 'OpenRouter connection successful',
                'model' => $this->openrouter_model,
                'response' => trim($response['choices'][0]['message']['content'])
            ];
        }

        return [
            'success' => false,
            'message' => 'OpenRouter connection failed',
            'response' => $response
        ];
    }

    /**
     * Save result image to media library
     */
    private function save_result_image(string $image_url): ?int {
        $response = wp_remote_get($image_url, ['timeout' => 60]);

        if (is_wp_error($response)) {
            return null;
        }

        $image_data = wp_remote_retrieve_body($response);
        $upload_dir = wp_upload_dir();

        // Create ai-styled-image/results directory
        $ai_results_dir = $upload_dir['basedir'] . '/ai-styled-image/results/';
        if (!wp_mkdir_p($ai_results_dir)) {
            return null;
        }

        $filename = 'ai-result-' . date('Y-m-d-H-i-s') . '-' . uniqid() . '.png';
        $file_path = $ai_results_dir . $filename;
        $file_url = $upload_dir['baseurl'] . '/ai-styled-image/results/' . $filename;

        if (file_put_contents($file_path, $image_data)) {
            $attachment = [
                'guid' => $file_url,
                'post_mime_type' => 'image/png',
                'post_title' => sanitize_file_name($filename),
                'post_content' => '',
                'post_status' => 'inherit'
            ];

            $attachment_id = wp_insert_attachment($attachment, $file_path);

            if (!is_wp_error($attachment_id)) {
                require_once(ABSPATH . 'wp-admin/includes/image.php');
                $attachment_data = wp_generate_attachment_metadata($attachment_id, $file_path);
                wp_update_attachment_metadata($attachment_id, $attachment_data);
                return $attachment_id;
            }
        }

        return null;
    }
    
    /**
     * Test mode response (when no API token)
     */
    private function test_mode_response(array $user_image, object $overlay): array {
        $upload_dir = wp_upload_dir();

        // Create ai-styled-image/results directory
        $ai_results_dir = $upload_dir['basedir'] . '/ai-styled-image/results/';
        if (!wp_mkdir_p($ai_results_dir)) {
            return $this->error('Failed to create results directory.');
        }

        $filename = 'test-' . date('Y-m-d-H-i-s') . '-' . uniqid() . '-' . sanitize_file_name($user_image['name']);
        $file_path = $ai_results_dir . $filename;
        $file_url = $upload_dir['baseurl'] . '/ai-styled-image/results/' . $filename;

        if (move_uploaded_file($user_image['tmp_name'], $file_path)) {
            $attachment = [
                'guid' => $file_url,
                'post_mime_type' => $user_image['type'],
                'post_title' => sanitize_file_name($filename),
                'post_content' => '',
                'post_status' => 'inherit'
            ];

            $attachment_id = wp_insert_attachment($attachment, $file_path);

            if (!is_wp_error($attachment_id)) {
                require_once(ABSPATH . 'wp-admin/includes/image.php');
                $attachment_data = wp_generate_attachment_metadata($attachment_id, $file_path);
                wp_update_attachment_metadata($attachment_id, $attachment_data);

                $this->update_usage_count($overlay->id);

                return $this->success([
                    'image_url' => wp_get_attachment_url($attachment_id),
                    'attachment_id' => $attachment_id,
                    'test_mode' => true,
                    'message' => 'Test mode: Original image returned. Configure API token for AI processing.'
                ]);
            }
        }

        return $this->error('Failed to process image in test mode.');
    }
    
    /**
     * Update overlay usage count
     */
    private function update_usage_count(int $overlay_id): void {
        global $wpdb;
        $wpdb->query(
            $wpdb->prepare(
                "UPDATE {$wpdb->prefix}ai_overlays SET usage_count = usage_count + 1 WHERE id = %d",
                $overlay_id
            )
        );
    }
    
    /**
     * Clean up temporary file
     */
    private function cleanup_temp_file(string $url): void {
        if (strpos($url, '/ai-temp/') !== false) {
            $upload_dir = wp_upload_dir();
            $file_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $url);
            
            if (file_exists($file_path)) {
                wp_delete_file($file_path);
            }
        }
    }
    
    /**
     * Delete uploaded file
     */
    private function delete_uploaded_file(string $url): void {
        if (strpos($url, '/ai-overlays/') !== false) {
            $upload_dir = wp_upload_dir();
            $file_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $url);
            
            if (file_exists($file_path)) {
                wp_delete_file($file_path);
            }
        }
    }
    
    /**
     * Handle AJAX overlay upload
     */
    public function ajax_upload_overlay(): void {
        check_ajax_referer('ai_styled_admin', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized access', 403);
        }

        $edit_id = intval($_POST['edit_id'] ?? 0);
        $media_id = intval($_POST['media_id'] ?? 0);

        // Log overlay upload attempt
        AI_Styled_Logger::log_user_action(
            'overlay_upload_attempt',
            'User attempted to upload/edit overlay',
            [
                'edit_id' => $edit_id,
                'media_id' => $media_id,
                'has_file' => isset($_FILES['overlay_image'])
            ]
        );

        // Handle edit mode
        if ($edit_id > 0) {
            $result = $this->update_overlay($edit_id, $_POST);

            if ($result['success']) {
                AI_Styled_Logger::log_user_action(
                    'overlay_updated',
                    "Overlay updated: {$_POST['title']}",
                    ['overlay_id' => $edit_id]
                );
            }

            wp_send_json($result);
            return;
        }

        // Handle media library upload
        if ($media_id > 0) {
            $result = $this->create_overlay_from_media($media_id, $_POST);

            if ($result['success']) {
                AI_Styled_Logger::log_user_action(
                    'overlay_created_from_media',
                    "Overlay created from media library: {$_POST['title']}",
                    ['media_id' => $media_id]
                );
            }

            wp_send_json($result);
            return;
        }

        // Handle direct file upload
        if (isset($_FILES['overlay_image'])) {
            $result = $this->upload_overlay($_FILES['overlay_image'], $_POST);

            if ($result['success']) {
                AI_Styled_Logger::log_user_action(
                    'overlay_uploaded',
                    "New overlay uploaded: {$_POST['title']}",
                    ['file_size' => $_FILES['overlay_image']['size']]
                );
            }

            wp_send_json($result);
            return;
        }

        AI_Styled_Logger::log_error('Overlay upload failed - no image provided');
        wp_send_json_error('No image provided');
    }
    
    /**
     * Handle AJAX overlay deletion
     */
    public function ajax_delete_overlay(): void {
        check_ajax_referer('ai_styled_admin', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized access', 403);
        }

        $overlay_id = intval($_POST['id'] ?? 0);

        // Get overlay info before deletion for logging
        $overlay = $this->get_overlay($overlay_id);
        $overlay_title = $overlay ? $overlay->title : "ID: {$overlay_id}";

        $result = $this->delete_overlay($overlay_id);

        if ($result['success']) {
            AI_Styled_Logger::log_user_action(
                'overlay_deleted',
                "Overlay deleted: {$overlay_title}",
                ['overlay_id' => $overlay_id]
            );
        } else {
            AI_Styled_Logger::log_error('Failed to delete overlay', [
                'overlay_id' => $overlay_id,
                'error' => $result['message'] ?? 'Unknown error'
            ]);
        }

        wp_send_json($result);
    }
    
    /**
     * Handle AJAX get overlay data
     */
    public function ajax_get_overlay(): void {
        check_ajax_referer('ai_styled_admin', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized access', 403);
        }
        
        $overlay_id = intval($_POST['id'] ?? 0);
        $overlay = $this->get_overlay($overlay_id);
        
        if ($overlay) {
            wp_send_json_success($overlay);
        } else {
            wp_send_json_error('Overlay not found');
        }
    }
    
    /**
     * Handle AJAX get overlays list
     */
    public function ajax_get_overlays(): void {
        check_ajax_referer('ai_styled_admin', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized access', 403);
        }
        
        $overlays = $this->get_overlays();
        $html = $this->render_overlays_grid($overlays);
        
        wp_send_json_success($html);
    }
    
    /**
     * Handle AJAX image processing
     */
    public function ajax_process_image(): void {
        check_ajax_referer('ai_styled_frontend', 'nonce');

        if (!isset($_FILES['user_image']) || !isset($_POST['overlay_id'])) {
            wp_send_json_error('Missing required data');
        }

        $overlay_id = intval($_POST['overlay_id']);
        $custom_prompt = sanitize_textarea_field($_POST['custom_prompt'] ?? '');

        // Log frontend processing attempt with configuration details
        AI_Styled_Logger::log_image_processing(
            'frontend_processing_started',
            'Frontend image processing started',
            [
                'overlay_id' => $overlay_id,
                'has_custom_prompt' => !empty($custom_prompt),
                'processing_mode' => $this->processing_mode,
                'has_openrouter_key' => !empty($this->openrouter_api_key),
                'openrouter_model' => $this->openrouter_model,
                'user_image_size' => $_FILES['user_image']['size'] ?? 0,
                'user_image_type' => $_FILES['user_image']['type'] ?? 'unknown'
            ]
        );

        $result = $this->process($_FILES['user_image'], $overlay_id, $custom_prompt);

        // Log the result
        AI_Styled_Logger::log_image_processing(
            'frontend_processing_completed',
            'Frontend image processing completed',
            [
                'success' => $result['success'],
                'has_logs' => isset($result['data']['logs']),
                'message' => $result['message'] ?? 'Success'
            ]
        );

        if ($result['success']) {
            wp_send_json_success($result['data']);
        } else {
            wp_send_json_error($result['message']);
        }
    }
    
    /**
     * Create overlay from media library
     */
    private function create_overlay_from_media(int $media_id, array $form_data): array {
        $attachment = get_post($media_id);
        
        if (!$attachment || $attachment->post_type !== 'attachment') {
            return $this->error('Invalid media attachment');
        }
        
        $file_path = get_attached_file($media_id);
        $mime_type = get_post_mime_type($media_id);
        
        if ($mime_type !== 'image/png') {
            return $this->error('Only PNG images are supported for overlays');
        }
        
        if (!file_exists($file_path)) {
            return $this->error('Media file not found');
        }
        
        // Copy to overlay directory
        $upload_dir = wp_upload_dir();
        $ai_dir = $upload_dir['basedir'] . '/ai-overlays/';
        
        if (!wp_mkdir_p($ai_dir)) {
            return $this->error('Failed to create upload directory');
        }
        
        $filename = 'overlay_' . uniqid() . '_' . basename($file_path);
        $new_path = $ai_dir . $filename;
        
        if (!copy($file_path, $new_path)) {
            return $this->error('Failed to copy file');
        }
        
        // Save to database
        $overlay_data = [
            'title' => sanitize_text_field($form_data['title'] ?? ''),
            'description' => sanitize_textarea_field($form_data['description'] ?? ''),
            'image_url' => $upload_dir['baseurl'] . '/ai-overlays/' . $filename,
            'category' => sanitize_text_field($form_data['category'] ?? 'general'),
            'prompt_template' => sanitize_textarea_field($form_data['prompt_template'] ?? ''),
            'created_at' => current_time('mysql')
        ];
        
        global $wpdb;
        $result = $wpdb->insert("{$wpdb->prefix}ai_overlays", $overlay_data);
        
        if ($result === false) {
            wp_delete_file($new_path);
            return $this->error('Failed to save overlay to database');
        }
        
        return $this->success([
            'message' => 'Overlay created successfully!',
            'overlay_id' => $wpdb->insert_id
        ]);
    }
    
    /**
     * Update existing overlay
     */
    private function update_overlay(int $overlay_id, array $form_data): array {
        global $wpdb;
        
        $overlay = $this->get_overlay($overlay_id);
        if (!$overlay) {
            return $this->error('Overlay not found');
        }
        
        $update_data = [
            'title' => sanitize_text_field($form_data['title'] ?? ''),
            'description' => sanitize_textarea_field($form_data['description'] ?? ''),
            'category' => sanitize_text_field($form_data['category'] ?? 'general'),
            'prompt_template' => sanitize_textarea_field($form_data['prompt_template'] ?? ''),
            'updated_at' => current_time('mysql')
        ];
        
        // Handle new image upload
        if (isset($_FILES['overlay_image']) && $_FILES['overlay_image']['size'] > 0) {
            $upload_result = $this->process_overlay_upload($_FILES['overlay_image']);
            if ($upload_result['success']) {
                // Delete old image
                $this->delete_uploaded_file($overlay->image_url);
                $update_data['image_url'] = $upload_result['url'];
            } else {
                return $upload_result;
            }
        }
        
        // Handle media library selection
        $media_id = intval($form_data['media_id'] ?? 0);
        if ($media_id > 0) {
            $attachment = get_post($media_id);
            if ($attachment && get_post_mime_type($media_id) === 'image/png') {
                $file_path = get_attached_file($media_id);
                $upload_dir = wp_upload_dir();
                $ai_dir = $upload_dir['basedir'] . '/ai-overlays/';
                
                wp_mkdir_p($ai_dir);
                
                $filename = 'overlay_' . uniqid() . '_' . basename($file_path);
                $new_path = $ai_dir . $filename;
                
                if (copy($file_path, $new_path)) {
                    $this->delete_uploaded_file($overlay->image_url);
                    $update_data['image_url'] = $upload_dir['baseurl'] . '/ai-overlays/' . $filename;
                }
            }
        }
        
        $result = $wpdb->update(
            "{$wpdb->prefix}ai_overlays",
            $update_data,
            ['id' => $overlay_id],
            ['%s', '%s', '%s', '%s', '%s'],
            ['%d']
        );
        
        if ($result === false) {
            return $this->error('Failed to update overlay');
        }
        
        return $this->success([
            'message' => 'Overlay updated successfully!',
            'overlay_id' => $overlay_id
        ]);
    }
    
    /**
     * Render overlays grid HTML
     */
    private function render_overlays_grid(array $overlays): string {
        if (empty($overlays)) {
            return '
                <div class="ai-empty-state">
                    <div class="ai-empty-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                            <circle cx="8.5" cy="8.5" r="1.5"/>
                            <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                        </svg>
                    </div>
                    <h3>No overlays yet</h3>
                    <p>Upload your first architectural overlay to get started</p>
                    <button type="button" class="ai-btn ai-btn-primary ai-empty-action">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 5v14M5 12h14"/>
                        </svg>
                        Add Your First Overlay
                    </button>
                </div>
            ';
        }
        
        $html = '';
        foreach ($overlays as $overlay) {
            $html .= sprintf('
                <div class="ai-overlay-card" data-id="%d">
                    <div class="ai-overlay-image">
                        <img src="%s" alt="%s">
                        <div class="ai-overlay-actions">
                            <button type="button" class="ai-action-btn ai-edit-overlay" data-id="%d" title="Edit">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                    <path d="m18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                </svg>
                            </button>
                            <button type="button" class="ai-action-btn ai-delete-overlay" data-id="%d" title="Delete">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="3,6 5,6 21,6"/>
                                    <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="ai-overlay-info">
                        <h3>%s</h3>
                        <p class="ai-overlay-description">%s</p>
                        <div class="ai-overlay-meta">
                            <span class="ai-category">%s</span>
                            <span class="ai-usage">Used %d times</span>
                        </div>
                    </div>
                </div>
            ',
                esc_attr($overlay->id),
                esc_url($overlay->image_url),
                esc_attr($overlay->title),
                esc_attr($overlay->id),
                esc_attr($overlay->id),
                esc_html($overlay->title),
                esc_html($overlay->description),
                esc_html(ucfirst(str_replace('_', ' ', $overlay->category))),
                intval($overlay->usage_count)
            );
        }
        
        return $html;
    }
    
    /**
     * Get public overlay data for frontend
     */
    public function get_overlay_data(int $overlay_id): ?object {
        return $this->get_overlay($overlay_id);
    }
    
    /**
     * Success response helper
     */
    private function success(array $data = []): array {
        // Include logging data if available
        if (!empty($this->processing_logs)) {
            $data['logs'] = $this->processing_logs;
        }

        return [
            'success' => true,
            'data' => $data
        ];
    }
    
    /**
     * Error response helper
     */
    private function error(string $message): array {
        return [
            'success' => false,
            'message' => $message
        ];
    }
} 